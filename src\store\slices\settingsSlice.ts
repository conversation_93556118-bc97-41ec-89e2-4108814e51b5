import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AppSettings, Theme } from '../../types';
import { settingsService } from '../../services/settingsService';
import { storageService } from '../../services/storageService';

interface SettingsState {
  settings: AppSettings;
  theme: Theme;
  isLoading: boolean;
  isSaving: boolean;
}

const defaultSettings: AppSettings = {
  theme: 'system',
  biometricEnabled: false,
  notifications: true,
  autoSync: true,
  defaultTaxRate: 8.5,
  defaultMarkupRate: 15,
  currency: 'USD',
  dateFormat: 'MM/DD/YYYY',
};

const lightTheme: Theme = {
  colors: {
    primary: '#2196F3',
    secondary: '#FF9800',
    background: '#FFFFFF',
    surface: '#F5F5F5',
    text: '#212121',
    textSecondary: '#757575',
    border: '#E0E0E0',
    error: '#F44336',
    warning: '#FF9800',
    success: '#4CAF50',
    info: '#2196F3',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  typography: {
    h1: 32,
    h2: 24,
    h3: 20,
    body: 16,
    caption: 12,
  },
  borderRadius: 8,
};

const darkTheme: Theme = {
  ...lightTheme,
  colors: {
    primary: '#2196F3',
    secondary: '#FF9800',
    background: '#121212',
    surface: '#1E1E1E',
    text: '#FFFFFF',
    textSecondary: '#B0B0B0',
    border: '#333333',
    error: '#F44336',
    warning: '#FF9800',
    success: '#4CAF50',
    info: '#2196F3',
  },
};

const initialState: SettingsState = {
  settings: defaultSettings,
  theme: lightTheme,
  isLoading: false,
  isSaving: false,
};

// Async thunks
export const loadSettings = createAsyncThunk(
  'settings/loadSettings',
  async (_, { rejectWithValue }) => {
    try {
      const storedSettings = await storageService.getSettings();
      return storedSettings || defaultSettings;
    } catch (error) {
      return rejectWithValue('Failed to load settings');
    }
  }
);

export const saveSettings = createAsyncThunk(
  'settings/saveSettings',
  async (settings: Partial<AppSettings>, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { settings: SettingsState };
      const updatedSettings = { ...state.settings.settings, ...settings };
      
      await storageService.setSettings(updatedSettings);
      
      // Also sync with server if user is authenticated
      const response = await settingsService.updateSettings(updatedSettings);
      if (response.success) {
        return updatedSettings;
      }
      
      // If server sync fails, still return local settings
      return updatedSettings;
    } catch (error) {
      return rejectWithValue('Failed to save settings');
    }
  }
);

export const syncSettings = createAsyncThunk(
  'settings/syncSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await settingsService.getSettings();
      if (response.success && response.data) {
        await storageService.setSettings(response.data);
        return response.data;
      }
      return rejectWithValue('Failed to sync settings');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const resetSettings = createAsyncThunk(
  'settings/resetSettings',
  async (_, { rejectWithValue }) => {
    try {
      await storageService.setSettings(defaultSettings);
      await settingsService.updateSettings(defaultSettings);
      return defaultSettings;
    } catch (error) {
      return rejectWithValue('Failed to reset settings');
    }
  }
);

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    updateLocalSettings: (state, action: PayloadAction<Partial<AppSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
      
      // Update theme if theme setting changed
      if (action.payload.theme) {
        settingsSlice.caseReducers.updateTheme(state, { 
          payload: action.payload.theme, 
          type: 'settings/updateTheme' 
        });
      }
    },
    updateTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      const themeMode = action.payload;
      
      if (themeMode === 'system') {
        // In a real app, you'd check system preference
        // For now, default to light
        state.theme = lightTheme;
      } else if (themeMode === 'dark') {
        state.theme = darkTheme;
      } else {
        state.theme = lightTheme;
      }
    },
    toggleBiometric: (state) => {
      state.settings.biometricEnabled = !state.settings.biometricEnabled;
    },
    toggleNotifications: (state) => {
      state.settings.notifications = !state.settings.notifications;
    },
    toggleAutoSync: (state) => {
      state.settings.autoSync = !state.settings.autoSync;
    },
    setTaxRate: (state, action: PayloadAction<number>) => {
      state.settings.defaultTaxRate = action.payload;
    },
    setMarkupRate: (state, action: PayloadAction<number>) => {
      state.settings.defaultMarkupRate = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.settings.currency = action.payload;
    },
    setDateFormat: (state, action: PayloadAction<string>) => {
      state.settings.dateFormat = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Load settings
      .addCase(loadSettings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.settings = action.payload;
        
        // Update theme based on loaded settings
        settingsSlice.caseReducers.updateTheme(state, { 
          payload: action.payload.theme, 
          type: 'settings/updateTheme' 
        });
      })
      .addCase(loadSettings.rejected, (state) => {
        state.isLoading = false;
      })
      // Save settings
      .addCase(saveSettings.pending, (state) => {
        state.isSaving = true;
      })
      .addCase(saveSettings.fulfilled, (state, action) => {
        state.isSaving = false;
        state.settings = action.payload;
        
        // Update theme if theme setting changed
        settingsSlice.caseReducers.updateTheme(state, { 
          payload: action.payload.theme, 
          type: 'settings/updateTheme' 
        });
      })
      .addCase(saveSettings.rejected, (state) => {
        state.isSaving = false;
      })
      // Sync settings
      .addCase(syncSettings.fulfilled, (state, action) => {
        state.settings = action.payload;
        
        // Update theme based on synced settings
        settingsSlice.caseReducers.updateTheme(state, { 
          payload: action.payload.theme, 
          type: 'settings/updateTheme' 
        });
      })
      // Reset settings
      .addCase(resetSettings.fulfilled, (state, action) => {
        state.settings = action.payload;
        state.theme = lightTheme; // Reset to light theme
      });
  },
});

export const {
  updateLocalSettings,
  updateTheme,
  toggleBiometric,
  toggleNotifications,
  toggleAutoSync,
  setTaxRate,
  setMarkupRate,
  setCurrency,
  setDateFormat,
} = settingsSlice.actions;

export default settingsSlice.reducer;
