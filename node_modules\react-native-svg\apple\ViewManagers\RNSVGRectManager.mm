/**
 * Copyright (c) 2015-present, <PERSON><PERSON><PERSON><PERSON>.
 * All rights reserved.
 *
 * This source code is licensed under the MIT-style license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import "RNSVGRectManager.h"

#import "RCTConvert+RNSVG.h"
#import "RNSVGRect.h"

@implementation RNSVGRectManager

RCT_EXPORT_MODULE()

- (RNSVGRenderable *)node
{
  return [RNSVGRect new];
}

RCT_EXPORT_VIEW_PROPERTY(x, RNSVGLength *)
RCT_EXPORT_VIEW_PROPERTY(y, RNSVGLength *)
RCT_CUSTOM_VIEW_PROPERTY(height, id, RNSVGRect)
{
  view.rectheight = [RCTConvert RNSVGLength:json];
}

RCT_CUSTOM_VIEW_PROPERTY(width, id, RNSVGRect)
{
  view.rectwidth = [RCTConvert RNSVGLength:json];
}
RCT_EXPORT_VIEW_PROPERTY(rx, R<PERSON><PERSON><PERSON><PERSON>ength *)
RCT_EXPORT_VIEW_PROPERTY(ry, RNSVGLength *)

@end
