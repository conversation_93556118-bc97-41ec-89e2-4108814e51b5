import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Estimate, EstimateFilters, PaginatedResponse, EstimateForm, LineItem } from '../../types';
import { estimateService } from '../../services/estimateService';

interface EstimateState {
  estimates: Estimate[];
  selectedEstimate: Estimate | null;
  currentEstimate: Partial<Estimate> | null; // For wizard
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  filters: EstimateFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  searchQuery: string;
}

const initialState: EstimateState = {
  estimates: [],
  selectedEstimate: null,
  currentEstimate: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
  },
  searchQuery: '',
};

// Async thunks
export const fetchEstimates = createAsyncThunk(
  'estimates/fetchEstimates',
  async (params: { page?: number; filters?: EstimateFilters; search?: string }, { rejectWithValue }) => {
    try {
      const response = await estimateService.getEstimates(params);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch estimates');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchEstimateById = createAsyncThunk(
  'estimates/fetchEstimateById',
  async (estimateId: string, { rejectWithValue }) => {
    try {
      const response = await estimateService.getEstimateById(estimateId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch estimate');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const createEstimate = createAsyncThunk(
  'estimates/createEstimate',
  async (estimateData: EstimateForm & { lineItems: LineItem[] }, { rejectWithValue }) => {
    try {
      const response = await estimateService.createEstimate(estimateData);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to create estimate');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const updateEstimate = createAsyncThunk(
  'estimates/updateEstimate',
  async ({ id, data }: { id: string; data: Partial<Estimate> }, { rejectWithValue }) => {
    try {
      const response = await estimateService.updateEstimate(id, data);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to update estimate');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const deleteEstimate = createAsyncThunk(
  'estimates/deleteEstimate',
  async (estimateId: string, { rejectWithValue }) => {
    try {
      const response = await estimateService.deleteEstimate(estimateId);
      if (response.success) {
        return estimateId;
      }
      return rejectWithValue(response.message || 'Failed to delete estimate');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const duplicateEstimate = createAsyncThunk(
  'estimates/duplicateEstimate',
  async (estimateId: string, { rejectWithValue }) => {
    try {
      const response = await estimateService.duplicateEstimate(estimateId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to duplicate estimate');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

const estimateSlice = createSlice({
  name: 'estimates',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<EstimateFilters>) => {
      state.filters = action.payload;
      state.pagination.page = 1;
    },
    clearFilters: (state) => {
      state.filters = {};
      state.pagination.page = 1;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      state.pagination.page = 1;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
      state.pagination.page = 1;
    },
    setSelectedEstimate: (state, action: PayloadAction<Estimate | null>) => {
      state.selectedEstimate = action.payload;
    },
    clearSelectedEstimate: (state) => {
      state.selectedEstimate = null;
    },
    // Wizard actions
    initializeCurrentEstimate: (state, action: PayloadAction<Partial<Estimate>>) => {
      state.currentEstimate = action.payload;
    },
    updateCurrentEstimate: (state, action: PayloadAction<Partial<Estimate>>) => {
      if (state.currentEstimate) {
        state.currentEstimate = { ...state.currentEstimate, ...action.payload };
      }
    },
    addLineItem: (state, action: PayloadAction<LineItem>) => {
      if (state.currentEstimate) {
        if (!state.currentEstimate.lineItems) {
          state.currentEstimate.lineItems = [];
        }
        state.currentEstimate.lineItems.push(action.payload);
        // Recalculate totals
        estimateSlice.caseReducers.recalculateTotals(state);
      }
    },
    updateLineItem: (state, action: PayloadAction<{ index: number; lineItem: LineItem }>) => {
      if (state.currentEstimate?.lineItems) {
        state.currentEstimate.lineItems[action.payload.index] = action.payload.lineItem;
        // Recalculate totals
        estimateSlice.caseReducers.recalculateTotals(state);
      }
    },
    removeLineItem: (state, action: PayloadAction<number>) => {
      if (state.currentEstimate?.lineItems) {
        state.currentEstimate.lineItems.splice(action.payload, 1);
        // Recalculate totals
        estimateSlice.caseReducers.recalculateTotals(state);
      }
    },
    recalculateTotals: (state) => {
      if (state.currentEstimate?.lineItems) {
        const subtotal = state.currentEstimate.lineItems.reduce(
          (sum, item) => sum + item.total,
          0
        );
        
        const taxRate = state.currentEstimate.taxRate || 0;
        const markupRate = state.currentEstimate.markupRate || 0;
        
        const taxAmount = subtotal * (taxRate / 100);
        const markupAmount = subtotal * (markupRate / 100);
        const total = subtotal + taxAmount + markupAmount;
        
        state.currentEstimate.subtotal = subtotal;
        state.currentEstimate.taxAmount = taxAmount;
        state.currentEstimate.markupAmount = markupAmount;
        state.currentEstimate.total = total;
      }
    },
    clearCurrentEstimate: (state) => {
      state.currentEstimate = null;
    },
    resetPagination: (state) => {
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
        hasMore: false,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch estimates
      .addCase(fetchEstimates.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchEstimates.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data, page, total, hasMore } = action.payload;
        
        if (page === 1) {
          state.estimates = data;
        } else {
          state.estimates = [...state.estimates, ...data];
        }
        
        state.pagination = {
          page,
          limit: state.pagination.limit,
          total,
          hasMore,
        };
      })
      .addCase(fetchEstimates.rejected, (state) => {
        state.isLoading = false;
      })
      // Fetch estimate by ID
      .addCase(fetchEstimateById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchEstimateById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedEstimate = action.payload;
      })
      .addCase(fetchEstimateById.rejected, (state) => {
        state.isLoading = false;
      })
      // Create estimate
      .addCase(createEstimate.pending, (state) => {
        state.isCreating = true;
      })
      .addCase(createEstimate.fulfilled, (state, action) => {
        state.isCreating = false;
        state.estimates.unshift(action.payload);
        state.pagination.total += 1;
        state.currentEstimate = null; // Clear wizard state
      })
      .addCase(createEstimate.rejected, (state) => {
        state.isCreating = false;
      })
      // Update estimate
      .addCase(updateEstimate.pending, (state) => {
        state.isUpdating = true;
      })
      .addCase(updateEstimate.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.estimates.findIndex(estimate => estimate.id === action.payload.id);
        if (index !== -1) {
          state.estimates[index] = action.payload;
        }
        if (state.selectedEstimate?.id === action.payload.id) {
          state.selectedEstimate = action.payload;
        }
      })
      .addCase(updateEstimate.rejected, (state) => {
        state.isUpdating = false;
      })
      // Delete estimate
      .addCase(deleteEstimate.fulfilled, (state, action) => {
        state.estimates = state.estimates.filter(estimate => estimate.id !== action.payload);
        state.pagination.total -= 1;
        if (state.selectedEstimate?.id === action.payload) {
          state.selectedEstimate = null;
        }
      })
      // Duplicate estimate
      .addCase(duplicateEstimate.fulfilled, (state, action) => {
        state.estimates.unshift(action.payload);
        state.pagination.total += 1;
      });
  },
});

export const {
  setFilters,
  clearFilters,
  setSearchQuery,
  clearSearchQuery,
  setSelectedEstimate,
  clearSelectedEstimate,
  initializeCurrentEstimate,
  updateCurrentEstimate,
  addLineItem,
  updateLineItem,
  removeLineItem,
  recalculateTotals,
  clearCurrentEstimate,
  resetPagination,
} = estimateSlice.actions;

export default estimateSlice.reducer;
