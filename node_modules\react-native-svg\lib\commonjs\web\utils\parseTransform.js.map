{"version": 3, "names": ["_extractTransform", "require", "parseTransformProp", "transform", "props", "transformArray", "push", "stringifyTransformProps", "Array", "isArray", "join", "stringifiedProps", "transformsArrayToProps", "length", "undefined", "transformProps", "translate", "translateX", "translateY", "scale", "scaleX", "scaleY", "rotation", "skewX", "skewY"], "sourceRoot": "../../../../src", "sources": ["web/utils/parseTransform.ts"], "mappings": ";;;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AAMO,SAASC,kBAAkBA,CAChCC,SAAsC,EACtCC,KAAiB,EACjB;EACA,MAAMC,cAAwB,GAAG,EAAE;EAEnCD,KAAK,IAAIC,cAAc,CAACC,IAAI,CAAC,GAAGC,uBAAuB,CAACH,KAAK,CAAC,CAAC;EAE/D,IAAII,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,EAAE;IAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpCE,cAAc,CAACC,IAAI,CAAC,UAAUH,SAAS,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACvD,CAAC,MAAM;MACL,MAAMC,gBAAgB,GAAG,IAAAC,wCAAsB;MAC7C;MACAT,SACF,CAAC;MACDE,cAAc,CAACC,IAAI,CAAC,GAAGC,uBAAuB,CAACI,gBAAgB,CAAC,CAAC;IACnE;EACF,CAAC,MAAM,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IACxCE,cAAc,CAACC,IAAI,CAACH,SAAS,CAAC;EAChC;EAEA,OAAOE,cAAc,CAACQ,MAAM,GAAGR,cAAc,CAACK,IAAI,CAAC,GAAG,CAAC,GAAGI,SAAS;AACrE;AAEO,SAASP,uBAAuBA,CAACQ,cAA8B,EAAE;EACtE,MAAMV,cAAc,GAAG,EAAE;EACzB,IAAIU,cAAc,CAACC,SAAS,IAAI,IAAI,EAAE;IACpCX,cAAc,CAACC,IAAI,CAAC,aAAaS,cAAc,CAACC,SAAS,GAAG,CAAC;EAC/D;EACA,IAAID,cAAc,CAACE,UAAU,IAAI,IAAI,IAAIF,cAAc,CAACG,UAAU,IAAI,IAAI,EAAE;IAC1Eb,cAAc,CAACC,IAAI,CACjB,aAAaS,cAAc,CAACE,UAAU,IAAI,CAAC,KACzCF,cAAc,CAACG,UAAU,IAAI,CAAC,GAElC,CAAC;EACH;EACA,IAAIH,cAAc,CAACI,KAAK,IAAI,IAAI,EAAE;IAChCd,cAAc,CAACC,IAAI,CAAC,SAASS,cAAc,CAACI,KAAK,GAAG,CAAC;EACvD;EACA,IAAIJ,cAAc,CAACK,MAAM,IAAI,IAAI,IAAIL,cAAc,CAACM,MAAM,IAAI,IAAI,EAAE;IAClEhB,cAAc,CAACC,IAAI,CACjB,SAASS,cAAc,CAACK,MAAM,IAAI,CAAC,KAAKL,cAAc,CAACM,MAAM,IAAI,CAAC,GACpE,CAAC;EACH;EACA;EACA,IAAIN,cAAc,CAACO,QAAQ,IAAI,IAAI,EAAE;IACnCjB,cAAc,CAACC,IAAI,CAAC,UAAUS,cAAc,CAACO,QAAQ,GAAG,CAAC;EAC3D;EACA,IAAIP,cAAc,CAACQ,KAAK,IAAI,IAAI,EAAE;IAChClB,cAAc,CAACC,IAAI,CAAC,SAASS,cAAc,CAACQ,KAAK,GAAG,CAAC;EACvD;EACA,IAAIR,cAAc,CAACS,KAAK,IAAI,IAAI,EAAE;IAChCnB,cAAc,CAACC,IAAI,CAAC,SAASS,cAAc,CAACS,KAAK,GAAG,CAAC;EACvD;EACA,OAAOnB,cAAc;AACvB", "ignoreList": []}