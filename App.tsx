/**
 * Construction Estimate Management App
 * Main App Component
 *
 * @format
 */

import React, { useEffect } from 'react';
import { StatusBar, useColorScheme } from 'react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { Provider as ReduxProvider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { store } from './src/store';
import { useAppDispatch, useAppSelector } from './src/store';
import { loadStoredAuth } from './src/store/slices/authSlice';
import { loadSettings } from './src/store/slices/settingsSlice';
import AppNavigator from './src/navigation/AppNavigator';
import LoadingScreen from './src/components/common/LoadingScreen';

function AppContent(): React.JSX.Element {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading: authLoading } = useAppSelector(state => state.auth);
  const { theme, isLoading: settingsLoading } = useAppSelector(state => state.settings);
  const colorScheme = useColorScheme();

  useEffect(() => {
    // Load stored authentication and settings on app start
    const initializeApp = async () => {
      await Promise.all([
        dispatch(loadStoredAuth()),
        dispatch(loadSettings()),
      ]);
    };

    initializeApp();
  }, [dispatch]);

  // Show loading screen while initializing
  if (authLoading || settingsLoading) {
    return <LoadingScreen />;
  }

  // Determine theme based on settings and system preference
  const isDarkMode = theme.colors.background === '#121212' ||
    (colorScheme === 'dark' && theme.colors.background !== '#FFFFFF');

  return (
    <PaperProvider theme={theme}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <StatusBar
          barStyle={isDarkMode ? 'light-content' : 'dark-content'}
          backgroundColor={theme.colors.background}
        />
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
      </GestureHandlerRootView>
    </PaperProvider>
  );
}

function App(): React.JSX.Element {
  return (
    <ReduxProvider store={store}>
      <AppContent />
    </ReduxProvider>
  );
}

export default App;
