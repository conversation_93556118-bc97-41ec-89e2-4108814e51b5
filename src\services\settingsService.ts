import { apiService } from './apiService';
import { AppSettings, ApiResponse } from '../types';

class SettingsService {
  async getSettings(): Promise<ApiResponse<AppSettings>> {
    try {
      return await apiService.get<AppSettings>('/settings');
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch settings',
      };
    }
  }

  async updateSettings(settings: Partial<AppSettings>): Promise<ApiResponse<AppSettings>> {
    try {
      return await apiService.patch<AppSettings>('/settings', settings);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update settings',
      };
    }
  }

  async resetSettings(): Promise<ApiResponse<AppSettings>> {
    try {
      return await apiService.post<AppSettings>('/settings/reset');
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to reset settings',
      };
    }
  }

  async exportSettings(): Promise<ApiResponse<{ url: string; filename: string }>> {
    try {
      return await apiService.post<{ url: string; filename: string }>('/settings/export');
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to export settings',
      };
    }
  }

  async importSettings(fileData: any): Promise<ApiResponse<AppSettings>> {
    try {
      return await apiService.upload<AppSettings>('/settings/import', fileData);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to import settings',
      };
    }
  }

  // Validation methods
  validateTaxRate(rate: number): { isValid: boolean; error?: string } {
    if (rate < 0 || rate > 100) {
      return {
        isValid: false,
        error: 'Tax rate must be between 0 and 100',
      };
    }
    return { isValid: true };
  }

  validateMarkupRate(rate: number): { isValid: boolean; error?: string } {
    if (rate < 0 || rate > 1000) {
      return {
        isValid: false,
        error: 'Markup rate must be between 0 and 1000',
      };
    }
    return { isValid: true };
  }

  validateCurrency(currency: string): { isValid: boolean; error?: string } {
    const supportedCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];
    if (!supportedCurrencies.includes(currency)) {
      return {
        isValid: false,
        error: 'Unsupported currency',
      };
    }
    return { isValid: true };
  }

  validateDateFormat(format: string): { isValid: boolean; error?: string } {
    const supportedFormats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'];
    if (!supportedFormats.includes(format)) {
      return {
        isValid: false,
        error: 'Unsupported date format',
      };
    }
    return { isValid: true };
  }

  // Default settings
  getDefaultSettings(): AppSettings {
    return {
      theme: 'system',
      biometricEnabled: false,
      notifications: true,
      autoSync: true,
      defaultTaxRate: 8.5,
      defaultMarkupRate: 15,
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
    };
  }

  // Settings migration
  migrateSettings(oldSettings: any, currentVersion: string): AppSettings {
    const defaultSettings = this.getDefaultSettings();
    
    // Handle migration from different versions
    switch (currentVersion) {
      case '1.0.0':
        // Migration logic for version 1.0.0
        return {
          ...defaultSettings,
          ...oldSettings,
        };
      default:
        return {
          ...defaultSettings,
          ...oldSettings,
        };
    }
  }
}

export const settingsService = new SettingsService();
