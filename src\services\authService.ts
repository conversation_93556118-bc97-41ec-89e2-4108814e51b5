import { apiService } from './apiService';
import { storageService } from './storageService';
import { LoginForm, RegisterForm, User, ApiResponse } from '../types';
import Keychain from 'react-native-keychain';
import ReactNativeBiometrics from 'react-native-biometrics';

class AuthService {
  private biometrics = new ReactNativeBiometrics();

  async login(credentials: LoginForm): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      const response = await apiService.post<{ user: User; token: string }>('/auth/login', credentials);
      
      if (response.success && response.data) {
        // Set token for future API calls
        apiService.setToken(response.data.token);
        
        // Store credentials securely if remember me is enabled
        if (credentials.rememberMe) {
          await this.storeCredentials(credentials.email, credentials.password);
        }
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  async register(userData: RegisterForm): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      const response = await apiService.post<{ user: User; token: string }>('/auth/register', userData);
      
      if (response.success && response.data) {
        // Set token for future API calls
        apiService.setToken(response.data.token);
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Registration failed',
      };
    }
  }

  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate token on server
      await apiService.post('/auth/logout');
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      // Clear local data regardless of API call result
      apiService.clearToken();
      await this.clearStoredCredentials();
    }
  }

  async forgotPassword(email: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post('/auth/forgot-password', { email });
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Password reset failed',
      };
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post('/auth/reset-password', { token, password: newPassword });
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Password reset failed',
      };
    }
  }

  async verifyToken(token: string): Promise<boolean> {
    try {
      apiService.setToken(token);
      const response = await apiService.get('/auth/verify');
      return response.success;
    } catch (error) {
      return false;
    }
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    try {
      return await apiService.post<{ token: string }>('/auth/refresh');
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Token refresh failed',
      };
    }
  }

  // Biometric Authentication
  async isBiometricAvailable(): Promise<boolean> {
    try {
      const { available } = await this.biometrics.isSensorAvailable();
      return available;
    } catch (error) {
      console.error('Biometric availability check failed:', error);
      return false;
    }
  }

  async getBiometricType(): Promise<string | null> {
    try {
      const { biometryType } = await this.biometrics.isSensorAvailable();
      return biometryType || null;
    } catch (error) {
      console.error('Biometric type check failed:', error);
      return null;
    }
  }

  async enableBiometric(): Promise<boolean> {
    try {
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        throw new Error('Biometric authentication is not available');
      }

      // Create biometric key
      const { success } = await this.biometrics.createKeys();
      if (!success) {
        throw new Error('Failed to create biometric keys');
      }

      // Test biometric authentication
      const { success: authSuccess } = await this.biometrics.simplePrompt({
        promptMessage: 'Authenticate to enable biometric login',
        cancelButtonText: 'Cancel',
      });

      return authSuccess;
    } catch (error) {
      console.error('Biometric setup failed:', error);
      return false;
    }
  }

  async disableBiometric(): Promise<void> {
    try {
      await this.biometrics.deleteKeys();
      await storageService.setBiometricEnabled(false);
    } catch (error) {
      console.error('Biometric disable failed:', error);
    }
  }

  async authenticateWithBiometric(): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      const isAvailable = await this.isBiometricAvailable();
      if (!isAvailable) {
        return {
          success: false,
          message: 'Biometric authentication is not available',
        };
      }

      // Authenticate with biometrics
      const { success, signature } = await this.biometrics.createSignature({
        promptMessage: 'Authenticate with biometrics',
        payload: 'biometric_login',
        cancelButtonText: 'Cancel',
      });

      if (!success || !signature) {
        return {
          success: false,
          message: 'Biometric authentication failed',
        };
      }

      // Verify signature with server
      const response = await apiService.post<{ user: User; token: string }>('/auth/biometric', {
        signature,
        payload: 'biometric_login',
      });

      if (response.success && response.data) {
        apiService.setToken(response.data.token);
      }

      return response;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Biometric authentication failed',
      };
    }
  }

  // Secure credential storage
  private async storeCredentials(email: string, password: string): Promise<void> {
    try {
      await Keychain.setCredentials('EstimateApp', email, password, {
        accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
        authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
      });
    } catch (error) {
      console.error('Failed to store credentials:', error);
    }
  }

  async getStoredCredentials(): Promise<{ email: string; password: string } | null> {
    try {
      const credentials = await Keychain.getCredentials('EstimateApp');
      if (credentials && credentials.username && credentials.password) {
        return {
          email: credentials.username,
          password: credentials.password,
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to retrieve credentials:', error);
      return null;
    }
  }

  private async clearStoredCredentials(): Promise<void> {
    try {
      await Keychain.resetCredentials('EstimateApp');
    } catch (error) {
      console.error('Failed to clear credentials:', error);
    }
  }

  // Password validation
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Email validation
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await storageService.getToken();
      if (!token) return false;
      
      return await this.verifyToken(token);
    } catch (error) {
      return false;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await apiService.get<User>('/auth/me');
      return response.success && response.data ? response.data : null;
    } catch (error) {
      return null;
    }
  }

  // Update user profile
  async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      return await apiService.patch<User>('/auth/profile', userData);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Profile update failed',
      };
    }
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post('/auth/change-password', {
        currentPassword,
        newPassword,
      });
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Password change failed',
      };
    }
  }
}

export const authService = new AuthService();
