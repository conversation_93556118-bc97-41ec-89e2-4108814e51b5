import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DashboardMetrics, ActivityItem, ChartData } from '../../types';
import { dashboardService } from '../../services/dashboardService';

interface DashboardState {
  metrics: DashboardMetrics | null;
  activities: ActivityItem[];
  chartData: {
    revenue: ChartData | null;
    estimates: ChartData | null;
    conversion: ChartData | null;
  };
  isLoading: boolean;
  isLoadingActivities: boolean;
  isLoadingCharts: boolean;
  dateRange: {
    start: string;
    end: string;
  };
  refreshInterval: number | null;
}

const initialState: DashboardState = {
  metrics: null,
  activities: [],
  chartData: {
    revenue: null,
    estimates: null,
    conversion: null,
  },
  isLoading: false,
  isLoadingActivities: false,
  isLoadingCharts: false,
  dateRange: {
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
    end: new Date().toISOString(),
  },
  refreshInterval: null,
};

// Async thunks
export const fetchDashboardMetrics = createAsyncThunk(
  'dashboard/fetchMetrics',
  async (dateRange: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getMetrics(dateRange);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch metrics');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchRecentActivities = createAsyncThunk(
  'dashboard/fetchActivities',
  async (limit: number = 20, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getRecentActivities(limit);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch activities');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchRevenueChart = createAsyncThunk(
  'dashboard/fetchRevenueChart',
  async (dateRange: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getRevenueChart(dateRange);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch revenue chart');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchEstimatesChart = createAsyncThunk(
  'dashboard/fetchEstimatesChart',
  async (dateRange: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getEstimatesChart(dateRange);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch estimates chart');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchConversionChart = createAsyncThunk(
  'dashboard/fetchConversionChart',
  async (dateRange: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getConversionChart(dateRange);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch conversion chart');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const refreshDashboard = createAsyncThunk(
  'dashboard/refresh',
  async (_, { dispatch, getState }) => {
    const state = getState() as { dashboard: DashboardState };
    const { dateRange } = state.dashboard;
    
    // Fetch all dashboard data
    await Promise.all([
      dispatch(fetchDashboardMetrics(dateRange)),
      dispatch(fetchRecentActivities()),
      dispatch(fetchRevenueChart(dateRange)),
      dispatch(fetchEstimatesChart(dateRange)),
      dispatch(fetchConversionChart(dateRange)),
    ]);
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setDateRange: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.dateRange = action.payload;
    },
    setRefreshInterval: (state, action: PayloadAction<number | null>) => {
      state.refreshInterval = action.payload;
    },
    addActivity: (state, action: PayloadAction<ActivityItem>) => {
      state.activities.unshift(action.payload);
      // Keep only the latest 50 activities
      if (state.activities.length > 50) {
        state.activities = state.activities.slice(0, 50);
      }
    },
    clearActivities: (state) => {
      state.activities = [];
    },
    updateMetrics: (state, action: PayloadAction<Partial<DashboardMetrics>>) => {
      if (state.metrics) {
        state.metrics = { ...state.metrics, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch metrics
      .addCase(fetchDashboardMetrics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchDashboardMetrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.metrics = action.payload;
      })
      .addCase(fetchDashboardMetrics.rejected, (state) => {
        state.isLoading = false;
      })
      // Fetch activities
      .addCase(fetchRecentActivities.pending, (state) => {
        state.isLoadingActivities = true;
      })
      .addCase(fetchRecentActivities.fulfilled, (state, action) => {
        state.isLoadingActivities = false;
        state.activities = action.payload;
      })
      .addCase(fetchRecentActivities.rejected, (state) => {
        state.isLoadingActivities = false;
      })
      // Fetch revenue chart
      .addCase(fetchRevenueChart.pending, (state) => {
        state.isLoadingCharts = true;
      })
      .addCase(fetchRevenueChart.fulfilled, (state, action) => {
        state.isLoadingCharts = false;
        state.chartData.revenue = action.payload;
      })
      .addCase(fetchRevenueChart.rejected, (state) => {
        state.isLoadingCharts = false;
      })
      // Fetch estimates chart
      .addCase(fetchEstimatesChart.fulfilled, (state, action) => {
        state.chartData.estimates = action.payload;
      })
      // Fetch conversion chart
      .addCase(fetchConversionChart.fulfilled, (state, action) => {
        state.chartData.conversion = action.payload;
      });
  },
});

export const {
  setDateRange,
  setRefreshInterval,
  addActivity,
  clearActivities,
  updateMetrics,
} = dashboardSlice.actions;

export default dashboardSlice.reducer;
