import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  TextInput, 
  Button, 
  Card, 
  Checkbox, 
  useTheme,
  ActivityIndicator 
} from 'react-native-paper';
import { useFormik } from 'formik';
import * as yup from 'yup';
import { useAppDispatch, useAppSelector } from '../../store';
import { loginUser, authenticateWithBiometric } from '../../store/slices/authSlice';
import { LoginForm } from '../../types';

const validationSchema = yup.object().shape({
  email: yup
    .string()
    .email('Please enter a valid email')
    .required('Email is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

const LoginScreen: React.FC = () => {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { isLoading, biometricEnabled } = useAppSelector(state => state.auth);
  const [showPassword, setShowPassword] = useState(false);

  const formik = useFormik<LoginForm>({
    initialValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const result = await dispatch(loginUser(values));
        if (loginUser.rejected.match(result)) {
          Alert.alert('Login Failed', result.payload as string);
        }
      } catch (error) {
        Alert.alert('Error', 'An unexpected error occurred');
      }
    },
  });

  const handleBiometricLogin = async () => {
    try {
      const result = await dispatch(authenticateWithBiometric());
      if (authenticateWithBiometric.rejected.match(result)) {
        Alert.alert('Biometric Authentication Failed', result.payload as string);
      }
    } catch (error) {
      Alert.alert('Error', 'Biometric authentication failed');
    }
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.contentContainer}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.header}>
        <Text variant="headlineLarge" style={[styles.title, { color: theme.colors.primary }]}>
          EstimateApp
        </Text>
        <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onBackground }]}>
          Construction Estimate Management
        </Text>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          <Text variant="headlineSmall" style={styles.cardTitle}>
            Sign In
          </Text>

          <TextInput
            label="Email"
            value={formik.values.email}
            onChangeText={formik.handleChange('email')}
            onBlur={formik.handleBlur('email')}
            error={formik.touched.email && !!formik.errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            style={styles.input}
            left={<TextInput.Icon icon="email" />}
          />
          {formik.touched.email && formik.errors.email && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {formik.errors.email}
            </Text>
          )}

          <TextInput
            label="Password"
            value={formik.values.password}
            onChangeText={formik.handleChange('password')}
            onBlur={formik.handleBlur('password')}
            error={formik.touched.password && !!formik.errors.password}
            secureTextEntry={!showPassword}
            autoComplete="password"
            style={styles.input}
            left={<TextInput.Icon icon="lock" />}
            right={
              <TextInput.Icon 
                icon={showPassword ? "eye-off" : "eye"} 
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />
          {formik.touched.password && formik.errors.password && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {formik.errors.password}
            </Text>
          )}

          <View style={styles.checkboxContainer}>
            <Checkbox
              status={formik.values.rememberMe ? 'checked' : 'unchecked'}
              onPress={() => formik.setFieldValue('rememberMe', !formik.values.rememberMe)}
            />
            <Text variant="bodyMedium" style={styles.checkboxLabel}>
              Remember me
            </Text>
          </View>

          <Button
            mode="contained"
            onPress={formik.handleSubmit}
            disabled={isLoading || !formik.isValid}
            style={styles.button}
            contentStyle={styles.buttonContent}
          >
            {isLoading ? <ActivityIndicator color="white" /> : 'Sign In'}
          </Button>

          {biometricEnabled && (
            <Button
              mode="outlined"
              onPress={handleBiometricLogin}
              disabled={isLoading}
              style={styles.button}
              contentStyle={styles.buttonContent}
              icon="fingerprint"
            >
              Use Biometric
            </Button>
          )}

          <View style={styles.linkContainer}>
            <Button
              mode="text"
              onPress={() => {/* Navigate to forgot password */}}
              compact
            >
              Forgot Password?
            </Button>
          </View>
        </Card.Content>
      </Card>

      <View style={styles.footer}>
        <Text variant="bodyMedium" style={[styles.footerText, { color: theme.colors.onBackground }]}>
          Don't have an account?
        </Text>
        <Button
          mode="text"
          onPress={() => {/* Navigate to register */}}
          compact
        >
          Sign Up
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
  },
  card: {
    marginBottom: 20,
  },
  cardTitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 16,
    marginLeft: 16,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  checkboxLabel: {
    marginLeft: 8,
  },
  button: {
    marginVertical: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  linkContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    marginRight: 8,
  },
});

export default LoginScreen;
