import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, AppSettings } from '../types';

class StorageService {
  private readonly KEYS = {
    TOKEN: '@EstimateApp:token',
    USER: '@EstimateApp:user',
    SETTINGS: '@EstimateApp:settings',
    BIOMETRIC_ENABLED: '@EstimateApp:biometric_enabled',
    OFFLINE_DATA: '@EstimateApp:offline_data',
    LAST_SYNC: '@EstimateApp:last_sync',
  };

  // Token management
  async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.TOKEN, token);
    } catch (error) {
      console.error('Failed to store token:', error);
      throw new Error('Failed to store authentication token');
    }
  }

  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.KEYS.TOKEN);
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      return null;
    }
  }

  async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.KEYS.TOKEN);
    } catch (error) {
      console.error('Failed to remove token:', error);
    }
  }

  // User management
  async setUser(user: User): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.USER, JSON.stringify(user));
    } catch (error) {
      console.error('Failed to store user:', error);
      throw new Error('Failed to store user data');
    }
  }

  async getUser(): Promise<User | null> {
    try {
      const userData = await AsyncStorage.getItem(this.KEYS.USER);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to retrieve user:', error);
      return null;
    }
  }

  async removeUser(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.KEYS.USER);
    } catch (error) {
      console.error('Failed to remove user:', error);
    }
  }

  // Settings management
  async setSettings(settings: AppSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to store settings:', error);
      throw new Error('Failed to store settings');
    }
  }

  async getSettings(): Promise<AppSettings | null> {
    try {
      const settingsData = await AsyncStorage.getItem(this.KEYS.SETTINGS);
      return settingsData ? JSON.parse(settingsData) : null;
    } catch (error) {
      console.error('Failed to retrieve settings:', error);
      return null;
    }
  }

  // Biometric settings
  async setBiometricEnabled(enabled: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.BIOMETRIC_ENABLED, JSON.stringify(enabled));
    } catch (error) {
      console.error('Failed to store biometric setting:', error);
    }
  }

  async getBiometricEnabled(): Promise<boolean> {
    try {
      const biometricData = await AsyncStorage.getItem(this.KEYS.BIOMETRIC_ENABLED);
      return biometricData ? JSON.parse(biometricData) : false;
    } catch (error) {
      console.error('Failed to retrieve biometric setting:', error);
      return false;
    }
  }

  // Offline data management
  async setOfflineData(key: string, data: any): Promise<void> {
    try {
      const offlineKey = `${this.KEYS.OFFLINE_DATA}:${key}`;
      await AsyncStorage.setItem(offlineKey, JSON.stringify({
        data,
        timestamp: Date.now(),
      }));
    } catch (error) {
      console.error('Failed to store offline data:', error);
    }
  }

  async getOfflineData(key: string): Promise<any> {
    try {
      const offlineKey = `${this.KEYS.OFFLINE_DATA}:${key}`;
      const storedData = await AsyncStorage.getItem(offlineKey);
      
      if (storedData) {
        const parsed = JSON.parse(storedData);
        return parsed.data;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to retrieve offline data:', error);
      return null;
    }
  }

  async removeOfflineData(key: string): Promise<void> {
    try {
      const offlineKey = `${this.KEYS.OFFLINE_DATA}:${key}`;
      await AsyncStorage.removeItem(offlineKey);
    } catch (error) {
      console.error('Failed to remove offline data:', error);
    }
  }

  // Get all offline data keys
  async getOfflineDataKeys(): Promise<string[]> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      return allKeys
        .filter(key => key.startsWith(this.KEYS.OFFLINE_DATA))
        .map(key => key.replace(`${this.KEYS.OFFLINE_DATA}:`, ''));
    } catch (error) {
      console.error('Failed to get offline data keys:', error);
      return [];
    }
  }

  // Last sync timestamp
  async setLastSync(timestamp: number): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.LAST_SYNC, timestamp.toString());
    } catch (error) {
      console.error('Failed to store last sync timestamp:', error);
    }
  }

  async getLastSync(): Promise<number | null> {
    try {
      const timestamp = await AsyncStorage.getItem(this.KEYS.LAST_SYNC);
      return timestamp ? parseInt(timestamp, 10) : null;
    } catch (error) {
      console.error('Failed to retrieve last sync timestamp:', error);
      return null;
    }
  }

  // Clear all authentication data
  async clearAuth(): Promise<void> {
    try {
      await Promise.all([
        this.removeToken(),
        this.removeUser(),
        AsyncStorage.removeItem(this.KEYS.BIOMETRIC_ENABLED),
      ]);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }

  // Clear all app data
  async clearAll(): Promise<void> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const appKeys = allKeys.filter(key => key.startsWith('@EstimateApp:'));
      await AsyncStorage.multiRemove(appKeys);
    } catch (error) {
      console.error('Failed to clear all data:', error);
    }
  }

  // Get storage usage info
  async getStorageInfo(): Promise<{
    totalKeys: number;
    estimatedSize: number;
    lastSync: number | null;
  }> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const appKeys = allKeys.filter(key => key.startsWith('@EstimateApp:'));
      
      let estimatedSize = 0;
      for (const key of appKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          estimatedSize += value.length;
        }
      }

      const lastSync = await this.getLastSync();

      return {
        totalKeys: appKeys.length,
        estimatedSize,
        lastSync,
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        totalKeys: 0,
        estimatedSize: 0,
        lastSync: null,
      };
    }
  }

  // Batch operations
  async setMultiple(items: Array<[string, any]>): Promise<void> {
    try {
      const keyValuePairs: Array<[string, string]> = items.map(([key, value]) => [
        key,
        typeof value === 'string' ? value : JSON.stringify(value),
      ]);
      
      await AsyncStorage.multiSet(keyValuePairs);
    } catch (error) {
      console.error('Failed to set multiple items:', error);
      throw new Error('Failed to store multiple items');
    }
  }

  async getMultiple(keys: string[]): Promise<Record<string, any>> {
    try {
      const keyValuePairs = await AsyncStorage.multiGet(keys);
      const result: Record<string, any> = {};
      
      keyValuePairs.forEach(([key, value]) => {
        if (value) {
          try {
            result[key] = JSON.parse(value);
          } catch {
            result[key] = value;
          }
        }
      });
      
      return result;
    } catch (error) {
      console.error('Failed to get multiple items:', error);
      return {};
    }
  }
}

export const storageService = new StorageService();
