import { apiService } from './apiService';
import { storageService } from './storageService';
import { DashboardMetrics, ActivityItem, ChartData, ApiResponse } from '../types';

class DashboardService {
  private readonly OFFLINE_KEY = 'dashboard';

  async getMetrics(dateRange: { start: string; end: string }): Promise<ApiResponse<DashboardMetrics>> {
    try {
      const response = await apiService.get<DashboardMetrics>('/dashboard/metrics', dateRange);
      
      if (response.success && response.data) {
        await this.cacheMetrics(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedMetrics = await this.getCachedMetrics();
      if (cachedMetrics) {
        return {
          success: true,
          data: cachedMetrics,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch metrics',
      };
    }
  }

  async getRecentActivities(limit: number = 20): Promise<ApiResponse<ActivityItem[]>> {
    try {
      const response = await apiService.get<ActivityItem[]>('/dashboard/activities', { limit });
      
      if (response.success && response.data) {
        await this.cacheActivities(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedActivities = await this.getCachedActivities();
      if (cachedActivities.length > 0) {
        return {
          success: true,
          data: cachedActivities.slice(0, limit),
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch activities',
      };
    }
  }

  async getRevenueChart(dateRange: { start: string; end: string }): Promise<ApiResponse<ChartData>> {
    try {
      const response = await apiService.get<ChartData>('/dashboard/charts/revenue', dateRange);
      
      if (response.success && response.data) {
        await this.cacheChartData('revenue', response.data);
      }
      
      return response;
    } catch (error) {
      const cachedData = await this.getCachedChartData('revenue');
      if (cachedData) {
        return {
          success: true,
          data: cachedData,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch revenue chart',
      };
    }
  }

  async getEstimatesChart(dateRange: { start: string; end: string }): Promise<ApiResponse<ChartData>> {
    try {
      const response = await apiService.get<ChartData>('/dashboard/charts/estimates', dateRange);
      
      if (response.success && response.data) {
        await this.cacheChartData('estimates', response.data);
      }
      
      return response;
    } catch (error) {
      const cachedData = await this.getCachedChartData('estimates');
      if (cachedData) {
        return {
          success: true,
          data: cachedData,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch estimates chart',
      };
    }
  }

  async getConversionChart(dateRange: { start: string; end: string }): Promise<ApiResponse<ChartData>> {
    try {
      const response = await apiService.get<ChartData>('/dashboard/charts/conversion', dateRange);
      
      if (response.success && response.data) {
        await this.cacheChartData('conversion', response.data);
      }
      
      return response;
    } catch (error) {
      const cachedData = await this.getCachedChartData('conversion');
      if (cachedData) {
        return {
          success: true,
          data: cachedData,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch conversion chart',
      };
    }
  }

  async getEstimatesByStatus(): Promise<ApiResponse<{ status: string; count: number; value: number }[]>> {
    try {
      const response = await apiService.get<{ status: string; count: number; value: number }[]>('/dashboard/estimates-by-status');
      
      if (response.success && response.data) {
        await storageService.setOfflineData(`${this.OFFLINE_KEY}_estimates_by_status`, response.data);
      }
      
      return response;
    } catch (error) {
      const cachedData = await storageService.getOfflineData(`${this.OFFLINE_KEY}_estimates_by_status`);
      if (cachedData) {
        return {
          success: true,
          data: cachedData,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch estimates by status',
      };
    }
  }

  async getTopClients(limit: number = 10): Promise<ApiResponse<{ clientId: string; clientName: string; totalValue: number; estimateCount: number }[]>> {
    try {
      const response = await apiService.get<{ clientId: string; clientName: string; totalValue: number; estimateCount: number }[]>('/dashboard/top-clients', { limit });
      
      if (response.success && response.data) {
        await storageService.setOfflineData(`${this.OFFLINE_KEY}_top_clients`, response.data);
      }
      
      return response;
    } catch (error) {
      const cachedData = await storageService.getOfflineData(`${this.OFFLINE_KEY}_top_clients`);
      if (cachedData) {
        return {
          success: true,
          data: cachedData.slice(0, limit),
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch top clients',
      };
    }
  }

  async getRevenueByCategory(): Promise<ApiResponse<{ category: string; revenue: number; percentage: number }[]>> {
    try {
      const response = await apiService.get<{ category: string; revenue: number; percentage: number }[]>('/dashboard/revenue-by-category');
      
      if (response.success && response.data) {
        await storageService.setOfflineData(`${this.OFFLINE_KEY}_revenue_by_category`, response.data);
      }
      
      return response;
    } catch (error) {
      const cachedData = await storageService.getOfflineData(`${this.OFFLINE_KEY}_revenue_by_category`);
      if (cachedData) {
        return {
          success: true,
          data: cachedData,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch revenue by category',
      };
    }
  }

  async exportDashboardData(dateRange: { start: string; end: string }): Promise<ApiResponse<{ url: string; filename: string }>> {
    try {
      return await apiService.post<{ url: string; filename: string }>('/dashboard/export', dateRange);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to export dashboard data',
      };
    }
  }

  // Cache management
  private async cacheMetrics(metrics: DashboardMetrics): Promise<void> {
    try {
      await storageService.setOfflineData(`${this.OFFLINE_KEY}_metrics`, {
        ...metrics,
        cachedAt: Date.now(),
      });
    } catch (error) {
      console.error('Failed to cache metrics:', error);
    }
  }

  private async getCachedMetrics(): Promise<DashboardMetrics | null> {
    try {
      const cachedData = await storageService.getOfflineData(`${this.OFFLINE_KEY}_metrics`);
      if (cachedData) {
        // Check if cache is still valid (less than 5 minutes old)
        const cacheAge = Date.now() - cachedData.cachedAt;
        if (cacheAge < 5 * 60 * 1000) {
          const { cachedAt, ...metrics } = cachedData;
          return metrics;
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to get cached metrics:', error);
      return null;
    }
  }

  private async cacheActivities(activities: ActivityItem[]): Promise<void> {
    try {
      await storageService.setOfflineData(`${this.OFFLINE_KEY}_activities`, {
        activities,
        cachedAt: Date.now(),
      });
    } catch (error) {
      console.error('Failed to cache activities:', error);
    }
  }

  private async getCachedActivities(): Promise<ActivityItem[]> {
    try {
      const cachedData = await storageService.getOfflineData(`${this.OFFLINE_KEY}_activities`);
      if (cachedData) {
        // Check if cache is still valid (less than 2 minutes old)
        const cacheAge = Date.now() - cachedData.cachedAt;
        if (cacheAge < 2 * 60 * 1000) {
          return cachedData.activities;
        }
      }
      return [];
    } catch (error) {
      console.error('Failed to get cached activities:', error);
      return [];
    }
  }

  private async cacheChartData(chartType: string, data: ChartData): Promise<void> {
    try {
      await storageService.setOfflineData(`${this.OFFLINE_KEY}_chart_${chartType}`, {
        data,
        cachedAt: Date.now(),
      });
    } catch (error) {
      console.error('Failed to cache chart data:', error);
    }
  }

  private async getCachedChartData(chartType: string): Promise<ChartData | null> {
    try {
      const cachedData = await storageService.getOfflineData(`${this.OFFLINE_KEY}_chart_${chartType}`);
      if (cachedData) {
        // Check if cache is still valid (less than 10 minutes old)
        const cacheAge = Date.now() - cachedData.cachedAt;
        if (cacheAge < 10 * 60 * 1000) {
          return cachedData.data;
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to get cached chart data:', error);
      return null;
    }
  }

  // Generate mock data for offline use
  generateMockMetrics(): DashboardMetrics {
    return {
      activeEstimates: 15,
      conversionRate: 68.5,
      averageEstimateValue: 12500,
      totalRevenue: 187500,
      monthlyGrowth: 12.3,
    };
  }

  generateMockActivities(): ActivityItem[] {
    const now = Date.now();
    return [
      {
        id: '1',
        type: 'estimate_created',
        title: 'New Estimate Created',
        description: 'Kitchen Renovation estimate for John Smith',
        timestamp: new Date(now - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        userId: 'user1',
        entityId: 'estimate1',
      },
      {
        id: '2',
        type: 'estimate_approved',
        title: 'Estimate Approved',
        description: 'Bathroom Remodel estimate approved by client',
        timestamp: new Date(now - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        userId: 'user1',
        entityId: 'estimate2',
      },
      {
        id: '3',
        type: 'client_added',
        title: 'New Client Added',
        description: 'ABC Construction Company added to clients',
        timestamp: new Date(now - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
        userId: 'user1',
        entityId: 'client1',
      },
    ];
  }

  generateMockChartData(type: string): ChartData {
    const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    
    switch (type) {
      case 'revenue':
        return {
          labels,
          datasets: [{
            data: [15000, 18000, 22000, 19000, 25000, 28000],
            color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`,
            strokeWidth: 2,
          }],
        };
      case 'estimates':
        return {
          labels,
          datasets: [{
            data: [12, 15, 18, 16, 20, 22],
            color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
            strokeWidth: 2,
          }],
        };
      case 'conversion':
        return {
          labels,
          datasets: [{
            data: [65, 68, 72, 70, 75, 78],
            color: (opacity = 1) => `rgba(255, 152, 0, ${opacity})`,
            strokeWidth: 2,
          }],
        };
      default:
        return { labels: [], datasets: [] };
    }
  }
}

export const dashboardService = new DashboardService();
