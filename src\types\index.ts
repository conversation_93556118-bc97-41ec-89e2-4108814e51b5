// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: User<PERSON><PERSON>;
  companyId?: string;
  avatar?: string;
  phone?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'admin' | 'manager' | 'employee' | 'client';

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  biometricEnabled: boolean;
}

// Client Types
export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: Address;
  contactPerson?: string;
  category: ClientCategory;
  tags: string[];
  status: ClientStatus;
  projectHistory: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export type ClientCategory = 'residential' | 'commercial' | 'industrial' | 'government';
export type ClientStatus = 'active' | 'inactive' | 'prospect' | 'archived';

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

// Estimate Types
export interface Estimate {
  id: string;
  title: string;
  clientId: string;
  client?: Client;
  projectLocation: Address;
  status: EstimateStatus;
  lineItems: LineItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  markupRate: number;
  markupAmount: number;
  total: number;
  validUntil: string;
  notes?: string;
  templateId?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type EstimateStatus = 'draft' | 'pending' | 'approved' | 'rejected' | 'expired';

export interface LineItem {
  id: string;
  description: string;
  category: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  total: number;
  notes?: string;
}

// Template Types
export interface Template {
  id: string;
  name: string;
  description?: string;
  category: TemplateCategory;
  lineItems: TemplateLineItem[];
  version: number;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type TemplateCategory = 
  | 'electrical' 
  | 'plumbing' 
  | 'hvac' 
  | 'roofing' 
  | 'flooring' 
  | 'painting' 
  | 'general' 
  | 'custom';

export interface TemplateLineItem {
  id: string;
  description: string;
  category: string;
  defaultQuantity: number;
  unit: string;
  defaultUnitPrice: number;
  notes?: string;
}

// Dashboard and Analytics Types
export interface DashboardMetrics {
  activeEstimates: number;
  conversionRate: number;
  averageEstimateValue: number;
  totalRevenue: number;
  monthlyGrowth: number;
}

export interface ActivityItem {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  timestamp: string;
  userId: string;
  entityId?: string;
}

export type ActivityType = 
  | 'estimate_created' 
  | 'estimate_updated' 
  | 'estimate_approved' 
  | 'client_added' 
  | 'template_created';

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }[];
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  EstimateWizard: { estimateId?: string; templateId?: string };
  ClientDetail: { clientId: string };
  EstimateDetail: { estimateId: string };
  TemplateEditor: { templateId?: string };
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  BiometricSetup: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Estimates: undefined;
  Clients: undefined;
  Templates: undefined;
  Profile: undefined;
};

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  companyName?: string;
}

export interface ClientForm {
  name: string;
  email: string;
  phone: string;
  address: Address;
  contactPerson?: string;
  category: ClientCategory;
  notes?: string;
}

export interface EstimateForm {
  title: string;
  clientId: string;
  projectLocation: Address;
  validUntil: string;
  notes?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Filter and Search Types
export interface ClientFilters {
  category?: ClientCategory;
  status?: ClientStatus;
  tags?: string[];
  search?: string;
}

export interface EstimateFilters {
  status?: EstimateStatus;
  clientId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
}

// Theme Types
export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

export interface Theme {
  colors: ThemeColors;
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: number;
    h2: number;
    h3: number;
    body: number;
    caption: number;
  };
  borderRadius: number;
}

// Settings Types
export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  biometricEnabled: boolean;
  notifications: boolean;
  autoSync: boolean;
  defaultTaxRate: number;
  defaultMarkupRate: number;
  currency: string;
  dateFormat: string;
}
