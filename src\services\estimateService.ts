import { apiService } from './apiService';
import { storageService } from './storageService';
import { Estimate, EstimateForm, EstimateFilters, PaginatedResponse, ApiResponse, LineItem } from '../types';

class EstimateService {
  private readonly OFFLINE_KEY = 'estimates';

  async getEstimates(params: {
    page?: number;
    filters?: EstimateFilters;
    search?: string;
  } = {}): Promise<ApiResponse<PaginatedResponse<Estimate>>> {
    try {
      const response = await apiService.get<PaginatedResponse<Estimate>>('/estimates', params);
      
      if (response.success && response.data) {
        await this.cacheEstimates(response.data.data);
      }
      
      return response;
    } catch (error) {
      const cachedEstimates = await this.getCachedEstimates();
      if (cachedEstimates.length > 0) {
        return {
          success: true,
          data: {
            data: this.filterCachedEstimates(cachedEstimates, params),
            total: cachedEstimates.length,
            page: params.page || 1,
            limit: 20,
            hasMore: false,
          },
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch estimates',
      };
    }
  }

  async getEstimateById(estimateId: string): Promise<ApiResponse<Estimate>> {
    try {
      const response = await apiService.get<Estimate>(`/estimates/${estimateId}`);
      
      if (response.success && response.data) {
        await this.cacheEstimate(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedEstimate = await this.getCachedEstimate(estimateId);
      if (cachedEstimate) {
        return {
          success: true,
          data: cachedEstimate,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch estimate',
      };
    }
  }

  async createEstimate(estimateData: EstimateForm & { lineItems: LineItem[] }): Promise<ApiResponse<Estimate>> {
    try {
      // Calculate totals before sending
      const calculatedData = this.calculateTotals(estimateData);
      const response = await apiService.post<Estimate>('/estimates', calculatedData);
      
      if (response.success && response.data) {
        await this.cacheEstimate(response.data);
      }
      
      return response;
    } catch (error) {
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('create', estimateData);
        
        const tempEstimate: Estimate = {
          id: `temp_${Date.now()}`,
          ...this.calculateTotals(estimateData),
          status: 'draft',
          createdBy: 'current_user', // Should come from auth state
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        await this.cacheEstimate(tempEstimate);
        
        return {
          success: true,
          data: tempEstimate,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create estimate',
      };
    }
  }

  async updateEstimate(estimateId: string, estimateData: Partial<Estimate>): Promise<ApiResponse<Estimate>> {
    try {
      // Recalculate totals if line items changed
      const calculatedData = estimateData.lineItems 
        ? this.calculateTotals(estimateData as any)
        : estimateData;
        
      const response = await apiService.patch<Estimate>(`/estimates/${estimateId}`, calculatedData);
      
      if (response.success && response.data) {
        await this.cacheEstimate(response.data);
      }
      
      return response;
    } catch (error) {
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('update', { id: estimateId, ...estimateData });
        
        const cachedEstimate = await this.getCachedEstimate(estimateId);
        if (cachedEstimate) {
          const updatedEstimate = { 
            ...cachedEstimate, 
            ...estimateData, 
            updatedAt: new Date().toISOString() 
          };
          await this.cacheEstimate(updatedEstimate);
          
          return {
            success: true,
            data: updatedEstimate,
          };
        }
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update estimate',
      };
    }
  }

  async deleteEstimate(estimateId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiService.delete(`/estimates/${estimateId}`);
      
      if (response.success) {
        await this.removeCachedEstimate(estimateId);
      }
      
      return response;
    } catch (error) {
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('delete', { id: estimateId });
        await this.removeCachedEstimate(estimateId);
        
        return {
          success: true,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete estimate',
      };
    }
  }

  async duplicateEstimate(estimateId: string): Promise<ApiResponse<Estimate>> {
    try {
      const response = await apiService.post<Estimate>(`/estimates/${estimateId}/duplicate`);
      
      if (response.success && response.data) {
        await this.cacheEstimate(response.data);
      }
      
      return response;
    } catch (error) {
      // Try to duplicate from cached data
      const cachedEstimate = await this.getCachedEstimate(estimateId);
      if (cachedEstimate) {
        const duplicatedEstimate: Estimate = {
          ...cachedEstimate,
          id: `temp_${Date.now()}`,
          title: `${cachedEstimate.title} (Copy)`,
          status: 'draft',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        await this.cacheEstimate(duplicatedEstimate);
        
        return {
          success: true,
          data: duplicatedEstimate,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to duplicate estimate',
      };
    }
  }

  async generatePDF(estimateId: string): Promise<ApiResponse<{ url: string }>> {
    try {
      return await apiService.post<{ url: string }>(`/estimates/${estimateId}/pdf`);
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to generate PDF',
      };
    }
  }

  async sendEstimate(estimateId: string, recipientEmail: string, message?: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.post(`/estimates/${estimateId}/send`, {
        recipientEmail,
        message,
      });
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send estimate',
      };
    }
  }

  // Calculation utilities
  private calculateTotals(estimateData: any): any {
    if (!estimateData.lineItems || estimateData.lineItems.length === 0) {
      return {
        ...estimateData,
        subtotal: 0,
        taxAmount: 0,
        markupAmount: 0,
        total: 0,
      };
    }

    const subtotal = estimateData.lineItems.reduce(
      (sum: number, item: LineItem) => sum + (item.quantity * item.unitPrice),
      0
    );

    const taxRate = estimateData.taxRate || 0;
    const markupRate = estimateData.markupRate || 0;

    const taxAmount = subtotal * (taxRate / 100);
    const markupAmount = subtotal * (markupRate / 100);
    const total = subtotal + taxAmount + markupAmount;

    return {
      ...estimateData,
      subtotal,
      taxAmount,
      markupAmount,
      total,
    };
  }

  // Cache management
  private async cacheEstimates(estimates: Estimate[]): Promise<void> {
    try {
      const existingEstimates = await this.getCachedEstimates();
      const estimateMap = new Map(existingEstimates.map(estimate => [estimate.id, estimate]));
      
      estimates.forEach(estimate => estimateMap.set(estimate.id, estimate));
      
      await storageService.setOfflineData(this.OFFLINE_KEY, Array.from(estimateMap.values()));
    } catch (error) {
      console.error('Failed to cache estimates:', error);
    }
  }

  private async cacheEstimate(estimate: Estimate): Promise<void> {
    try {
      const existingEstimates = await this.getCachedEstimates();
      const estimateIndex = existingEstimates.findIndex(e => e.id === estimate.id);
      
      if (estimateIndex >= 0) {
        existingEstimates[estimateIndex] = estimate;
      } else {
        existingEstimates.push(estimate);
      }
      
      await storageService.setOfflineData(this.OFFLINE_KEY, existingEstimates);
    } catch (error) {
      console.error('Failed to cache estimate:', error);
    }
  }

  private async getCachedEstimates(): Promise<Estimate[]> {
    try {
      const cachedData = await storageService.getOfflineData(this.OFFLINE_KEY);
      return cachedData || [];
    } catch (error) {
      console.error('Failed to get cached estimates:', error);
      return [];
    }
  }

  private async getCachedEstimate(estimateId: string): Promise<Estimate | null> {
    try {
      const cachedEstimates = await this.getCachedEstimates();
      return cachedEstimates.find(estimate => estimate.id === estimateId) || null;
    } catch (error) {
      console.error('Failed to get cached estimate:', error);
      return null;
    }
  }

  private async removeCachedEstimate(estimateId: string): Promise<void> {
    try {
      const existingEstimates = await this.getCachedEstimates();
      const filteredEstimates = existingEstimates.filter(estimate => estimate.id !== estimateId);
      await storageService.setOfflineData(this.OFFLINE_KEY, filteredEstimates);
    } catch (error) {
      console.error('Failed to remove cached estimate:', error);
    }
  }

  // Offline action management
  private async storeOfflineAction(action: string, data: any): Promise<void> {
    try {
      const offlineActions = await storageService.getOfflineData('estimate_actions') || [];
      offlineActions.push({
        id: Date.now().toString(),
        action,
        data,
        timestamp: Date.now(),
      });
      await storageService.setOfflineData('estimate_actions', offlineActions);
    } catch (error) {
      console.error('Failed to store offline action:', error);
    }
  }

  // Utility methods
  private filterCachedEstimates(estimates: Estimate[], params: any): Estimate[] {
    let filtered = [...estimates];
    
    if (params.search) {
      const query = params.search.toLowerCase();
      filtered = filtered.filter(estimate =>
        estimate.title.toLowerCase().includes(query) ||
        estimate.client?.name.toLowerCase().includes(query)
      );
    }
    
    if (params.filters?.status) {
      filtered = filtered.filter(estimate => estimate.status === params.filters.status);
    }
    
    if (params.filters?.clientId) {
      filtered = filtered.filter(estimate => estimate.clientId === params.filters.clientId);
    }
    
    if (params.filters?.dateRange) {
      const { start, end } = params.filters.dateRange;
      filtered = filtered.filter(estimate => {
        const estimateDate = new Date(estimate.createdAt);
        return estimateDate >= new Date(start) && estimateDate <= new Date(end);
      });
    }
    
    return filtered;
  }

  private isNetworkError(error: any): boolean {
    return error instanceof Error && (
      error.message.includes('Network') ||
      error.message.includes('fetch') ||
      error.message.includes('connection')
    );
  }

  // Sync offline actions when back online
  async syncOfflineActions(): Promise<void> {
    try {
      const offlineActions = await storageService.getOfflineData('estimate_actions') || [];
      
      for (const action of offlineActions) {
        try {
          switch (action.action) {
            case 'create':
              await this.createEstimate(action.data);
              break;
            case 'update':
              await this.updateEstimate(action.data.id, action.data);
              break;
            case 'delete':
              await this.deleteEstimate(action.data.id);
              break;
          }
        } catch (error) {
          console.error('Failed to sync offline action:', error);
        }
      }
      
      await storageService.removeOfflineData('estimate_actions');
    } catch (error) {
      console.error('Failed to sync offline actions:', error);
    }
  }
}

export const estimateService = new EstimateService();
