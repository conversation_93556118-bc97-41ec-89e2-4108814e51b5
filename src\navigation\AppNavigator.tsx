import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useAppSelector } from '../store';
import { RootStackParamList } from '../types';

import AuthNavigator from './AuthNavigator';
import MainNavigator from './MainNavigator';
import EstimateWizardScreen from '../screens/estimates/EstimateWizardScreen';
import ClientDetailScreen from '../screens/clients/ClientDetailScreen';
import EstimateDetailScreen from '../screens/estimates/EstimateDetailScreen';
import TemplateEditorScreen from '../screens/templates/TemplateEditorScreen';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const { isAuthenticated } = useAppSelector(state => state.auth);

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!isAuthenticated ? (
        // Auth Stack
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : (
        // Main App Stack
        <>
          <Stack.Screen name="Main" component={MainNavigator} />
          <Stack.Screen 
            name="EstimateWizard" 
            component={EstimateWizardScreen}
            options={{
              headerShown: true,
              title: 'Create Estimate',
              presentation: 'modal',
            }}
          />
          <Stack.Screen 
            name="ClientDetail" 
            component={ClientDetailScreen}
            options={{
              headerShown: true,
              title: 'Client Details',
            }}
          />
          <Stack.Screen 
            name="EstimateDetail" 
            component={EstimateDetailScreen}
            options={{
              headerShown: true,
              title: 'Estimate Details',
            }}
          />
          <Stack.Screen 
            name="TemplateEditor" 
            component={TemplateEditorScreen}
            options={{
              headerShown: true,
              title: 'Template Editor',
              presentation: 'modal',
            }}
          />
        </>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
