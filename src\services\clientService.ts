import { apiService } from './apiService';
import { storageService } from './storageService';
import { Client, ClientForm, ClientFilters, PaginatedResponse, ApiResponse } from '../types';

class ClientService {
  private readonly OFFLINE_KEY = 'clients';

  async getClients(params: {
    page?: number;
    filters?: ClientFilters;
    search?: string;
  } = {}): Promise<ApiResponse<PaginatedResponse<Client>>> {
    try {
      const response = await apiService.get<PaginatedResponse<Client>>('/clients', params);
      
      // Cache successful response for offline use
      if (response.success && response.data) {
        await this.cacheClients(response.data.data);
      }
      
      return response;
    } catch (error) {
      // Try to return cached data if offline
      const cachedClients = await this.getCachedClients();
      if (cachedClients.length > 0) {
        return {
          success: true,
          data: {
            data: this.filterCachedClients(cachedClients, params),
            total: cachedClients.length,
            page: params.page || 1,
            limit: 20,
            hasMore: false,
          },
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch clients',
      };
    }
  }

  async getClientById(clientId: string): Promise<ApiResponse<Client>> {
    try {
      const response = await apiService.get<Client>(`/clients/${clientId}`);
      
      // Cache successful response
      if (response.success && response.data) {
        await this.cacheClient(response.data);
      }
      
      return response;
    } catch (error) {
      // Try to return cached client if offline
      const cachedClient = await this.getCachedClient(clientId);
      if (cachedClient) {
        return {
          success: true,
          data: cachedClient,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch client',
      };
    }
  }

  async createClient(clientData: ClientForm): Promise<ApiResponse<Client>> {
    try {
      const response = await apiService.post<Client>('/clients', clientData);
      
      // Cache successful response
      if (response.success && response.data) {
        await this.cacheClient(response.data);
      }
      
      return response;
    } catch (error) {
      // Store for offline sync if network error
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('create', clientData);
        
        // Create temporary client for immediate UI feedback
        const tempClient: Client = {
          id: `temp_${Date.now()}`,
          ...clientData,
          status: 'active',
          tags: clientData.tags || [],
          projectHistory: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        await this.cacheClient(tempClient);
        
        return {
          success: true,
          data: tempClient,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create client',
      };
    }
  }

  async updateClient(clientId: string, clientData: Partial<ClientForm>): Promise<ApiResponse<Client>> {
    try {
      const response = await apiService.patch<Client>(`/clients/${clientId}`, clientData);
      
      // Cache successful response
      if (response.success && response.data) {
        await this.cacheClient(response.data);
      }
      
      return response;
    } catch (error) {
      // Store for offline sync if network error
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('update', { id: clientId, ...clientData });
        
        // Update cached client for immediate UI feedback
        const cachedClient = await this.getCachedClient(clientId);
        if (cachedClient) {
          const updatedClient = { ...cachedClient, ...clientData, updatedAt: new Date().toISOString() };
          await this.cacheClient(updatedClient);
          
          return {
            success: true,
            data: updatedClient,
          };
        }
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update client',
      };
    }
  }

  async deleteClient(clientId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiService.delete(`/clients/${clientId}`);
      
      // Remove from cache
      if (response.success) {
        await this.removeCachedClient(clientId);
      }
      
      return response;
    } catch (error) {
      // Store for offline sync if network error
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('delete', { id: clientId });
        await this.removeCachedClient(clientId);
        
        return {
          success: true,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete client',
      };
    }
  }

  async searchClients(query: string): Promise<ApiResponse<PaginatedResponse<Client>>> {
    try {
      const response = await apiService.get<PaginatedResponse<Client>>('/clients/search', { q: query });
      return response;
    } catch (error) {
      // Search in cached clients if offline
      const cachedClients = await this.getCachedClients();
      const filteredClients = cachedClients.filter(client =>
        client.name.toLowerCase().includes(query.toLowerCase()) ||
        client.email.toLowerCase().includes(query.toLowerCase()) ||
        client.phone.includes(query)
      );
      
      return {
        success: true,
        data: {
          data: filteredClients,
          total: filteredClients.length,
          page: 1,
          limit: filteredClients.length,
          hasMore: false,
        },
      };
    }
  }

  // Cache management
  private async cacheClients(clients: Client[]): Promise<void> {
    try {
      const existingClients = await this.getCachedClients();
      const clientMap = new Map(existingClients.map(client => [client.id, client]));
      
      // Update with new clients
      clients.forEach(client => clientMap.set(client.id, client));
      
      await storageService.setOfflineData(this.OFFLINE_KEY, Array.from(clientMap.values()));
    } catch (error) {
      console.error('Failed to cache clients:', error);
    }
  }

  private async cacheClient(client: Client): Promise<void> {
    try {
      const existingClients = await this.getCachedClients();
      const clientIndex = existingClients.findIndex(c => c.id === client.id);
      
      if (clientIndex >= 0) {
        existingClients[clientIndex] = client;
      } else {
        existingClients.push(client);
      }
      
      await storageService.setOfflineData(this.OFFLINE_KEY, existingClients);
    } catch (error) {
      console.error('Failed to cache client:', error);
    }
  }

  private async getCachedClients(): Promise<Client[]> {
    try {
      const cachedData = await storageService.getOfflineData(this.OFFLINE_KEY);
      return cachedData || [];
    } catch (error) {
      console.error('Failed to get cached clients:', error);
      return [];
    }
  }

  private async getCachedClient(clientId: string): Promise<Client | null> {
    try {
      const cachedClients = await this.getCachedClients();
      return cachedClients.find(client => client.id === clientId) || null;
    } catch (error) {
      console.error('Failed to get cached client:', error);
      return null;
    }
  }

  private async removeCachedClient(clientId: string): Promise<void> {
    try {
      const existingClients = await this.getCachedClients();
      const filteredClients = existingClients.filter(client => client.id !== clientId);
      await storageService.setOfflineData(this.OFFLINE_KEY, filteredClients);
    } catch (error) {
      console.error('Failed to remove cached client:', error);
    }
  }

  // Offline action management
  private async storeOfflineAction(action: string, data: any): Promise<void> {
    try {
      const offlineActions = await storageService.getOfflineData('client_actions') || [];
      offlineActions.push({
        id: Date.now().toString(),
        action,
        data,
        timestamp: Date.now(),
      });
      await storageService.setOfflineData('client_actions', offlineActions);
    } catch (error) {
      console.error('Failed to store offline action:', error);
    }
  }

  // Utility methods
  private filterCachedClients(clients: Client[], params: any): Client[] {
    let filtered = [...clients];
    
    if (params.search) {
      const query = params.search.toLowerCase();
      filtered = filtered.filter(client =>
        client.name.toLowerCase().includes(query) ||
        client.email.toLowerCase().includes(query) ||
        client.phone.includes(query)
      );
    }
    
    if (params.filters?.category) {
      filtered = filtered.filter(client => client.category === params.filters.category);
    }
    
    if (params.filters?.status) {
      filtered = filtered.filter(client => client.status === params.filters.status);
    }
    
    if (params.filters?.tags?.length > 0) {
      filtered = filtered.filter(client =>
        params.filters.tags.some((tag: string) => client.tags.includes(tag))
      );
    }
    
    return filtered;
  }

  private isNetworkError(error: any): boolean {
    return error instanceof Error && (
      error.message.includes('Network') ||
      error.message.includes('fetch') ||
      error.message.includes('connection')
    );
  }

  // Sync offline actions when back online
  async syncOfflineActions(): Promise<void> {
    try {
      const offlineActions = await storageService.getOfflineData('client_actions') || [];
      
      for (const action of offlineActions) {
        try {
          switch (action.action) {
            case 'create':
              await this.createClient(action.data);
              break;
            case 'update':
              await this.updateClient(action.data.id, action.data);
              break;
            case 'delete':
              await this.deleteClient(action.data.id);
              break;
          }
        } catch (error) {
          console.error('Failed to sync offline action:', error);
        }
      }
      
      // Clear synced actions
      await storageService.removeOfflineData('client_actions');
    } catch (error) {
      console.error('Failed to sync offline actions:', error);
    }
  }
}

export const clientService = new ClientService();
