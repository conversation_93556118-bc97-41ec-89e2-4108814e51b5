import { apiService } from './apiService';
import { storageService } from './storageService';
import { Template, TemplateCategory, ApiResponse } from '../types';

class TemplateService {
  private readonly OFFLINE_KEY = 'templates';

  async getTemplates(params: {
    category?: TemplateCategory;
    search?: string;
  } = {}): Promise<ApiResponse<Template[]>> {
    try {
      const response = await apiService.get<Template[]>('/templates', params);
      
      if (response.success && response.data) {
        await this.cacheTemplates(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedTemplates = await this.getCachedTemplates();
      if (cachedTemplates.length > 0) {
        return {
          success: true,
          data: this.filterCachedTemplates(cachedTemplates, params),
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch templates',
      };
    }
  }

  async getTemplateById(templateId: string): Promise<ApiResponse<Template>> {
    try {
      const response = await apiService.get<Template>(`/templates/${templateId}`);
      
      if (response.success && response.data) {
        await this.cacheTemplate(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedTemplate = await this.getCachedTemplate(templateId);
      if (cachedTemplate) {
        return {
          success: true,
          data: cachedTemplate,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch template',
      };
    }
  }

  async createTemplate(templateData: Omit<Template, 'id' | 'version' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Template>> {
    try {
      const response = await apiService.post<Template>('/templates', templateData);
      
      if (response.success && response.data) {
        await this.cacheTemplate(response.data);
      }
      
      return response;
    } catch (error) {
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('create', templateData);
        
        const tempTemplate: Template = {
          id: `temp_${Date.now()}`,
          ...templateData,
          version: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        await this.cacheTemplate(tempTemplate);
        
        return {
          success: true,
          data: tempTemplate,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create template',
      };
    }
  }

  async updateTemplate(templateId: string, templateData: Partial<Template>): Promise<ApiResponse<Template>> {
    try {
      const response = await apiService.patch<Template>(`/templates/${templateId}`, templateData);
      
      if (response.success && response.data) {
        await this.cacheTemplate(response.data);
      }
      
      return response;
    } catch (error) {
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('update', { id: templateId, ...templateData });
        
        const cachedTemplate = await this.getCachedTemplate(templateId);
        if (cachedTemplate) {
          const updatedTemplate = { 
            ...cachedTemplate, 
            ...templateData, 
            version: cachedTemplate.version + 1,
            updatedAt: new Date().toISOString() 
          };
          await this.cacheTemplate(updatedTemplate);
          
          return {
            success: true,
            data: updatedTemplate,
          };
        }
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update template',
      };
    }
  }

  async deleteTemplate(templateId: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiService.delete(`/templates/${templateId}`);
      
      if (response.success) {
        await this.removeCachedTemplate(templateId);
      }
      
      return response;
    } catch (error) {
      if (this.isNetworkError(error)) {
        await this.storeOfflineAction('delete', { id: templateId });
        await this.removeCachedTemplate(templateId);
        
        return {
          success: true,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete template',
      };
    }
  }

  async duplicateTemplate(templateId: string): Promise<ApiResponse<Template>> {
    try {
      const response = await apiService.post<Template>(`/templates/${templateId}/duplicate`);
      
      if (response.success && response.data) {
        await this.cacheTemplate(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedTemplate = await this.getCachedTemplate(templateId);
      if (cachedTemplate) {
        const duplicatedTemplate: Template = {
          ...cachedTemplate,
          id: `temp_${Date.now()}`,
          name: `${cachedTemplate.name} (Copy)`,
          version: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        await this.cacheTemplate(duplicatedTemplate);
        
        return {
          success: true,
          data: duplicatedTemplate,
        };
      }
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to duplicate template',
      };
    }
  }

  async exportTemplates(templateIds: string[]): Promise<ApiResponse<{ url: string; filename: string }>> {
    try {
      return await apiService.post<{ url: string; filename: string }>('/templates/export', {
        templateIds,
      });
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to export templates',
      };
    }
  }

  async importTemplates(fileData: any): Promise<ApiResponse<Template[]>> {
    try {
      const response = await apiService.upload<Template[]>('/templates/import', fileData);
      
      if (response.success && response.data) {
        // Cache imported templates
        for (const template of response.data) {
          await this.cacheTemplate(template);
        }
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to import templates',
      };
    }
  }

  async getTemplatesByCategory(category: TemplateCategory): Promise<ApiResponse<Template[]>> {
    try {
      const response = await apiService.get<Template[]>(`/templates/category/${category}`);
      
      if (response.success && response.data) {
        await this.cacheTemplates(response.data);
      }
      
      return response;
    } catch (error) {
      const cachedTemplates = await this.getCachedTemplates();
      const filteredTemplates = cachedTemplates.filter(template => template.category === category);
      
      return {
        success: true,
        data: filteredTemplates,
      };
    }
  }

  // Cache management
  private async cacheTemplates(templates: Template[]): Promise<void> {
    try {
      const existingTemplates = await this.getCachedTemplates();
      const templateMap = new Map(existingTemplates.map(template => [template.id, template]));
      
      templates.forEach(template => templateMap.set(template.id, template));
      
      await storageService.setOfflineData(this.OFFLINE_KEY, Array.from(templateMap.values()));
    } catch (error) {
      console.error('Failed to cache templates:', error);
    }
  }

  private async cacheTemplate(template: Template): Promise<void> {
    try {
      const existingTemplates = await this.getCachedTemplates();
      const templateIndex = existingTemplates.findIndex(t => t.id === template.id);
      
      if (templateIndex >= 0) {
        existingTemplates[templateIndex] = template;
      } else {
        existingTemplates.push(template);
      }
      
      await storageService.setOfflineData(this.OFFLINE_KEY, existingTemplates);
    } catch (error) {
      console.error('Failed to cache template:', error);
    }
  }

  private async getCachedTemplates(): Promise<Template[]> {
    try {
      const cachedData = await storageService.getOfflineData(this.OFFLINE_KEY);
      return cachedData || [];
    } catch (error) {
      console.error('Failed to get cached templates:', error);
      return [];
    }
  }

  private async getCachedTemplate(templateId: string): Promise<Template | null> {
    try {
      const cachedTemplates = await this.getCachedTemplates();
      return cachedTemplates.find(template => template.id === templateId) || null;
    } catch (error) {
      console.error('Failed to get cached template:', error);
      return null;
    }
  }

  private async removeCachedTemplate(templateId: string): Promise<void> {
    try {
      const existingTemplates = await this.getCachedTemplates();
      const filteredTemplates = existingTemplates.filter(template => template.id !== templateId);
      await storageService.setOfflineData(this.OFFLINE_KEY, filteredTemplates);
    } catch (error) {
      console.error('Failed to remove cached template:', error);
    }
  }

  // Offline action management
  private async storeOfflineAction(action: string, data: any): Promise<void> {
    try {
      const offlineActions = await storageService.getOfflineData('template_actions') || [];
      offlineActions.push({
        id: Date.now().toString(),
        action,
        data,
        timestamp: Date.now(),
      });
      await storageService.setOfflineData('template_actions', offlineActions);
    } catch (error) {
      console.error('Failed to store offline action:', error);
    }
  }

  // Utility methods
  private filterCachedTemplates(templates: Template[], params: any): Template[] {
    let filtered = [...templates];
    
    if (params.search) {
      const query = params.search.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description?.toLowerCase().includes(query)
      );
    }
    
    if (params.category) {
      filtered = filtered.filter(template => template.category === params.category);
    }
    
    // Only return active templates
    filtered = filtered.filter(template => template.isActive);
    
    return filtered;
  }

  private isNetworkError(error: any): boolean {
    return error instanceof Error && (
      error.message.includes('Network') ||
      error.message.includes('fetch') ||
      error.message.includes('connection')
    );
  }

  // Sync offline actions when back online
  async syncOfflineActions(): Promise<void> {
    try {
      const offlineActions = await storageService.getOfflineData('template_actions') || [];
      
      for (const action of offlineActions) {
        try {
          switch (action.action) {
            case 'create':
              await this.createTemplate(action.data);
              break;
            case 'update':
              await this.updateTemplate(action.data.id, action.data);
              break;
            case 'delete':
              await this.deleteTemplate(action.data.id);
              break;
          }
        } catch (error) {
          console.error('Failed to sync offline action:', error);
        }
      }
      
      await storageService.removeOfflineData('template_actions');
    } catch (error) {
      console.error('Failed to sync offline actions:', error);
    }
  }
}

export const templateService = new TemplateService();
