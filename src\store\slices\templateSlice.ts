import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Template, TemplateCategory, TemplateLineItem } from '../../types';
import { templateService } from '../../services/templateService';

interface TemplateState {
  templates: Template[];
  selectedTemplate: Template | null;
  currentTemplate: Partial<Template> | null; // For editor
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  categories: TemplateCategory[];
  searchQuery: string;
  selectedCategory: TemplateCategory | null;
}

const initialState: TemplateState = {
  templates: [],
  selectedTemplate: null,
  currentTemplate: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  categories: ['electrical', 'plumbing', 'hvac', 'roofing', 'flooring', 'painting', 'general', 'custom'],
  searchQuery: '',
  selectedCategory: null,
};

// Async thunks
export const fetchTemplates = createAsyncThunk(
  'templates/fetchTemplates',
  async (params: { category?: TemplateCategory; search?: string }, { rejectWithValue }) => {
    try {
      const response = await templateService.getTemplates(params);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch templates');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchTemplateById = createAsyncThunk(
  'templates/fetchTemplateById',
  async (templateId: string, { rejectWithValue }) => {
    try {
      const response = await templateService.getTemplateById(templateId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch template');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const createTemplate = createAsyncThunk(
  'templates/createTemplate',
  async (templateData: Omit<Template, 'id' | 'version' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await templateService.createTemplate(templateData);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to create template');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const updateTemplate = createAsyncThunk(
  'templates/updateTemplate',
  async ({ id, data }: { id: string; data: Partial<Template> }, { rejectWithValue }) => {
    try {
      const response = await templateService.updateTemplate(id, data);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to update template');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const deleteTemplate = createAsyncThunk(
  'templates/deleteTemplate',
  async (templateId: string, { rejectWithValue }) => {
    try {
      const response = await templateService.deleteTemplate(templateId);
      if (response.success) {
        return templateId;
      }
      return rejectWithValue(response.message || 'Failed to delete template');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const duplicateTemplate = createAsyncThunk(
  'templates/duplicateTemplate',
  async (templateId: string, { rejectWithValue }) => {
    try {
      const response = await templateService.duplicateTemplate(templateId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to duplicate template');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const exportTemplates = createAsyncThunk(
  'templates/exportTemplates',
  async (templateIds: string[], { rejectWithValue }) => {
    try {
      const response = await templateService.exportTemplates(templateIds);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to export templates');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const importTemplates = createAsyncThunk(
  'templates/importTemplates',
  async (fileData: any, { rejectWithValue }) => {
    try {
      const response = await templateService.importTemplates(fileData);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to import templates');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

const templateSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    setSelectedCategory: (state, action: PayloadAction<TemplateCategory | null>) => {
      state.selectedCategory = action.payload;
    },
    clearSelectedCategory: (state) => {
      state.selectedCategory = null;
    },
    setSelectedTemplate: (state, action: PayloadAction<Template | null>) => {
      state.selectedTemplate = action.payload;
    },
    clearSelectedTemplate: (state) => {
      state.selectedTemplate = null;
    },
    // Editor actions
    initializeCurrentTemplate: (state, action: PayloadAction<Partial<Template>>) => {
      state.currentTemplate = action.payload;
    },
    updateCurrentTemplate: (state, action: PayloadAction<Partial<Template>>) => {
      if (state.currentTemplate) {
        state.currentTemplate = { ...state.currentTemplate, ...action.payload };
      }
    },
    addTemplateLineItem: (state, action: PayloadAction<TemplateLineItem>) => {
      if (state.currentTemplate) {
        if (!state.currentTemplate.lineItems) {
          state.currentTemplate.lineItems = [];
        }
        state.currentTemplate.lineItems.push(action.payload);
      }
    },
    updateTemplateLineItem: (state, action: PayloadAction<{ index: number; lineItem: TemplateLineItem }>) => {
      if (state.currentTemplate?.lineItems) {
        state.currentTemplate.lineItems[action.payload.index] = action.payload.lineItem;
      }
    },
    removeTemplateLineItem: (state, action: PayloadAction<number>) => {
      if (state.currentTemplate?.lineItems) {
        state.currentTemplate.lineItems.splice(action.payload, 1);
      }
    },
    clearCurrentTemplate: (state) => {
      state.currentTemplate = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch templates
      .addCase(fetchTemplates.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchTemplates.fulfilled, (state, action) => {
        state.isLoading = false;
        state.templates = action.payload;
      })
      .addCase(fetchTemplates.rejected, (state) => {
        state.isLoading = false;
      })
      // Fetch template by ID
      .addCase(fetchTemplateById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchTemplateById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedTemplate = action.payload;
      })
      .addCase(fetchTemplateById.rejected, (state) => {
        state.isLoading = false;
      })
      // Create template
      .addCase(createTemplate.pending, (state) => {
        state.isCreating = true;
      })
      .addCase(createTemplate.fulfilled, (state, action) => {
        state.isCreating = false;
        state.templates.unshift(action.payload);
        state.currentTemplate = null; // Clear editor state
      })
      .addCase(createTemplate.rejected, (state) => {
        state.isCreating = false;
      })
      // Update template
      .addCase(updateTemplate.pending, (state) => {
        state.isUpdating = true;
      })
      .addCase(updateTemplate.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.templates.findIndex(template => template.id === action.payload.id);
        if (index !== -1) {
          state.templates[index] = action.payload;
        }
        if (state.selectedTemplate?.id === action.payload.id) {
          state.selectedTemplate = action.payload;
        }
      })
      .addCase(updateTemplate.rejected, (state) => {
        state.isUpdating = false;
      })
      // Delete template
      .addCase(deleteTemplate.fulfilled, (state, action) => {
        state.templates = state.templates.filter(template => template.id !== action.payload);
        if (state.selectedTemplate?.id === action.payload) {
          state.selectedTemplate = null;
        }
      })
      // Duplicate template
      .addCase(duplicateTemplate.fulfilled, (state, action) => {
        state.templates.unshift(action.payload);
      })
      // Import templates
      .addCase(importTemplates.fulfilled, (state, action) => {
        state.templates = [...state.templates, ...action.payload];
      });
  },
});

export const {
  setSearchQuery,
  clearSearchQuery,
  setSelectedCategory,
  clearSelectedCategory,
  setSelectedTemplate,
  clearSelectedTemplate,
  initializeCurrentTemplate,
  updateCurrentTemplate,
  addTemplateLineItem,
  updateTemplateLineItem,
  removeTemplateLineItem,
  clearCurrentTemplate,
} = templateSlice.actions;

export default templateSlice.reducer;
