import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Client, ClientFilters, PaginatedResponse, ClientForm } from '../../types';
import { clientService } from '../../services/clientService';

interface ClientState {
  clients: Client[];
  selectedClient: Client | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  filters: ClientFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  searchQuery: string;
}

const initialState: ClientState = {
  clients: [],
  selectedClient: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
  },
  searchQuery: '',
};

// Async thunks
export const fetchClients = createAsyncThunk(
  'clients/fetchClients',
  async (params: { page?: number; filters?: ClientFilters; search?: string }, { rejectWithValue }) => {
    try {
      const response = await clientService.getClients(params);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch clients');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchClientById = createAsyncThunk(
  'clients/fetchClientById',
  async (clientId: string, { rejectWithValue }) => {
    try {
      const response = await clientService.getClientById(clientId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch client');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const createClient = createAsyncThunk(
  'clients/createClient',
  async (clientData: ClientForm, { rejectWithValue }) => {
    try {
      const response = await clientService.createClient(clientData);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to create client');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const updateClient = createAsyncThunk(
  'clients/updateClient',
  async ({ id, data }: { id: string; data: Partial<ClientForm> }, { rejectWithValue }) => {
    try {
      const response = await clientService.updateClient(id, data);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to update client');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const deleteClient = createAsyncThunk(
  'clients/deleteClient',
  async (clientId: string, { rejectWithValue }) => {
    try {
      const response = await clientService.deleteClient(clientId);
      if (response.success) {
        return clientId;
      }
      return rejectWithValue(response.message || 'Failed to delete client');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const searchClients = createAsyncThunk(
  'clients/searchClients',
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await clientService.searchClients(query);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Search failed');
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

const clientSlice = createSlice({
  name: 'clients',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<ClientFilters>) => {
      state.filters = action.payload;
      state.pagination.page = 1; // Reset to first page when filters change
    },
    clearFilters: (state) => {
      state.filters = {};
      state.pagination.page = 1;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      state.pagination.page = 1;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
      state.pagination.page = 1;
    },
    setSelectedClient: (state, action: PayloadAction<Client | null>) => {
      state.selectedClient = action.payload;
    },
    clearSelectedClient: (state) => {
      state.selectedClient = null;
    },
    resetPagination: (state) => {
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
        hasMore: false,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch clients
      .addCase(fetchClients.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchClients.fulfilled, (state, action) => {
        state.isLoading = false;
        const { data, page, total, hasMore } = action.payload;
        
        if (page === 1) {
          state.clients = data;
        } else {
          state.clients = [...state.clients, ...data];
        }
        
        state.pagination = {
          page,
          limit: state.pagination.limit,
          total,
          hasMore,
        };
      })
      .addCase(fetchClients.rejected, (state) => {
        state.isLoading = false;
      })
      // Fetch client by ID
      .addCase(fetchClientById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchClientById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedClient = action.payload;
      })
      .addCase(fetchClientById.rejected, (state) => {
        state.isLoading = false;
      })
      // Create client
      .addCase(createClient.pending, (state) => {
        state.isCreating = true;
      })
      .addCase(createClient.fulfilled, (state, action) => {
        state.isCreating = false;
        state.clients.unshift(action.payload);
        state.pagination.total += 1;
      })
      .addCase(createClient.rejected, (state) => {
        state.isCreating = false;
      })
      // Update client
      .addCase(updateClient.pending, (state) => {
        state.isUpdating = true;
      })
      .addCase(updateClient.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.clients.findIndex(client => client.id === action.payload.id);
        if (index !== -1) {
          state.clients[index] = action.payload;
        }
        if (state.selectedClient?.id === action.payload.id) {
          state.selectedClient = action.payload;
        }
      })
      .addCase(updateClient.rejected, (state) => {
        state.isUpdating = false;
      })
      // Delete client
      .addCase(deleteClient.fulfilled, (state, action) => {
        state.clients = state.clients.filter(client => client.id !== action.payload);
        state.pagination.total -= 1;
        if (state.selectedClient?.id === action.payload) {
          state.selectedClient = null;
        }
      })
      // Search clients
      .addCase(searchClients.fulfilled, (state, action) => {
        state.clients = action.payload.data;
        state.pagination = {
          page: 1,
          limit: state.pagination.limit,
          total: action.payload.total,
          hasMore: action.payload.hasMore,
        };
      });
  },
});

export const {
  setFilters,
  clearFilters,
  setSearchQuery,
  clearSearchQuery,
  setSelectedClient,
  clearSelectedClient,
  resetPagination,
} = clientSlice.actions;

export default clientSlice.reducer;
